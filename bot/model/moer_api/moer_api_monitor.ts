import { <PERSON>rAP<PERSON> } from './moer'
import logger from '../logger/logger'

/**
 * 墨尔API监控工具
 * 用于监控API状态和性能
 */
export class MoerAPIMonitor {
  private static monitorInterval: NodeJS.Timeout | null = null
  private static readonly MONITOR_INTERVAL = 60000 // 1分钟检查一次

  /**
   * 开始监控
   */
  public static startMonitoring() {
    if (this.monitorInterval) {
      return // 已经在监控中
    }

    logger.log('开始墨尔API监控')
    
    this.monitorInterval = setInterval(async () => {
      await this.performHealthCheck()
    }, this.MONITOR_INTERVAL)

    // 立即执行一次检查
    this.performHealthCheck()
  }

  /**
   * 停止监控
   */
  public static stopMonitoring() {
    if (this.monitorInterval) {
      clearInterval(this.monitorInterval)
      this.monitorInterval = null
      logger.log('停止墨尔API监控')
    }
  }

  /**
   * 执行健康检查
   */
  private static async performHealthCheck() {
    try {
      const isHealthy = await MoerAPI.healthCheck()
      const status = MoerAPI.getCircuitBreakerStatus()

      logger.log('墨尔API健康检查结果', {
        isHealthy,
        circuitBreakerState: status.state,
        failureCount: status.failureCount,
        timestamp: new Date().toISOString()
      })

      // 如果API不健康且熔断器开启，发送警报
      if (!isHealthy && status.state === 'OPEN') {
        await this.sendAlert({
          level: 'ERROR',
          message: '墨尔API服务异常，熔断器已开启',
          details: status
        })
      }

    } catch (error) {
      logger.error('健康检查执行失败', { error: error.message })
    }
  }

  /**
   * 发送警报（可以扩展为钉钉、邮件等）
   */
  private static async sendAlert(alert: {
    level: 'WARNING' | 'ERROR'
    message: string
    details: any
  }) {
    logger.error(`🚨 墨尔API警报 [${alert.level}]`, {
      message: alert.message,
      details: alert.details,
      timestamp: new Date().toISOString()
    })

    // TODO: 这里可以添加钉钉机器人、邮件通知等
    // await DingTalkBot.sendAlert(alert)
    // await EmailService.sendAlert(alert)
  }

  /**
   * 获取监控状态
   */
  public static getMonitorStatus() {
    return {
      isMonitoring: this.monitorInterval !== null,
      apiStatus: MoerAPI.getCircuitBreakerStatus(),
      monitorInterval: this.MONITOR_INTERVAL
    }
  }

  /**
   * 手动触发健康检查
   */
  public static async manualHealthCheck() {
    logger.log('手动触发墨尔API健康检查')
    await this.performHealthCheck()
    return this.getMonitorStatus()
  }
}

// 自动启动监控（可选）
// MoerAPIMonitor.startMonitoring()
